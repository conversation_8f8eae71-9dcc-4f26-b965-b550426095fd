//
//  TextStyle.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/18.
//

import Foundation
import SwiftUI

// 是否拥有宠物的标签显示
struct OwnershipBadge: View {
    let isOwned: Bool
    
    var body: some View {
        Text(isOwned ? "已获得" : "未获得")
            .font(.caption)
            .bold()
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 5)
            .background(isOwned ? Color.green.opacity(0.8) : Color.gray.opacity(0.8))
            .clipShape(Capsule())
    }
}
