import Foundation
import Combine
import UIKit

class CarbonPetViewModel: ObservableObject {
    // 发布的数据
    @Published var petTemplates: [PetTemplate] = []
    @Published var userPets: [UserPet] = []
    @Published var displayModels: [PetDisplayModel] = []
    @Published var isLoading = false

    // 本地存储 Key
    private let userPetsKey = "user_pets_v2"

    init() {
        loadPetTemplates()
    }

    /// 加载宠物图鉴模板（从 Bundle JSON）
    func loadPetTemplates() {
        guard let url = Bundle.main.url(forResource: "pets", withExtension: "json"),
              let data = try? Data(contentsOf: url) else {
            print("Failed to find or load pets.json")
            return
        }

        let decoder = JSONDecoder()
        do {
            let templates = try decoder.decode([PetTemplate].self, from: data)
            DispatchQueue.main.async {
                self.petTemplates = templates
                self.loadUserPets()
                self.updateDisplayModels()
            }
        } catch {
            print("Failed to decode pets.json: \(error)")
        }
    }

    /// 加载用户已获得宠物（从 UserDefaults）
    func loadUserPets() {
        if let data = UserDefaults.standard.data(forKey: userPetsKey),
           let pets = try? JSONDecoder().decode([UserPet].self, from: data) {
            userPets = pets
        } else {
            // 开发阶段：自动解锁第一只宠物作为 Mock 数据
            if let firstTemplate = petTemplates.first {
                let firstUserPet = UserPet(templateName: firstTemplate.name)
                userPets = [firstUserPet]
                saveUserPets()
            }
        }
    }

    /// 保存用户宠物数据（到 UserDefaults）
    func saveUserPets() {
        if let data = try? JSONEncoder().encode(userPets) {
            UserDefaults.standard.set(data, forKey: userPetsKey)
        }
    }

    /// 更新显示模型
    func updateDisplayModels() {
        displayModels = petTemplates.map { template in
            let userPet = userPets.first { $0.templateName == template.name }
            return PetDisplayModel(template: template, userPet: userPet)
        }
    }

    /// 喂养宠物
    func feedPet(userPetId: UUID, experience: Int = 10) {
        if let index = userPets.firstIndex(where: { $0.id == userPetId }) {
            userPets[index].feed(experience: experience)
            if userPets[index].canLevelUp {
                userPets[index].levelUp()
            }
            saveUserPets()
            updateDisplayModels()
        }
    }

    /// 解锁新宠物（通过模板名称）
    func unlockPet(templateName: String) {
        guard petTemplates.contains(where: { $0.name == templateName }),
              !userPets.contains(where: { $0.templateName == templateName }) else {
            return
        }

        let newUserPet = UserPet(templateName: templateName)
        userPets.append(newUserPet)
        saveUserPets()
        updateDisplayModels()
    }

    /// 检查用户是否拥有特定宠物
    func isPetOwned(templateName: String) -> Bool {
        return userPets.contains(where: { $0.templateName == templateName })
    }

    /// 根据模板名称获取用户宠物
    func getUserPet(templateName: String) -> UserPet? {
        return userPets.first(where: { $0.templateName == templateName })
    }

    /// 根据模板名称获取宠物模板
    func getPetTemplate(name: String) -> PetTemplate? {
        return petTemplates.first(where: { $0.name == name })
    }

    /// 获取宠物图像（从 Assets）
    func getPetImage(imageName: String) -> UIImage? {
        return UIImage(named: imageName)
    }

    /// 根据ID获取宠物显示模型
    func getDisplayModel(by id: UUID) -> PetDisplayModel? {
        return displayModels.first(where: { $0.id == id })
    }

    /// 根据模板名称获取宠物显示模型
    func getDisplayModel(by templateName: String) -> PetDisplayModel? {
        return displayModels.first(where: { $0.template.name == templateName })
    }

    /// 获取宠物的详细信息（用于详情页面）
    func getPetDetailInfo(for displayModel: PetDisplayModel) -> (template: PetTemplate, userPet: UserPet?, isOwned: Bool) {
        return (
            template: displayModel.template,
            userPet: displayModel.userPet,
            isOwned: displayModel.isOwned
        )
    }

    /// 获取宠物的装扮信息（占位方法，后续扩展）
    func getPetOutfits(for templateName: String) -> [String] {
        // 暂时返回占位数据
        return ["礼冠套装", "花冠套装", "魔法师套装"]
    }

    /// 切换到下一个宠物（用于详情页面的左右切换）
    func getNextPet(current: PetDisplayModel) -> PetDisplayModel? {
        guard let currentIndex = displayModels.firstIndex(where: { $0.id == current.id }) else {
            return nil
        }
        let nextIndex = (currentIndex + 1) % displayModels.count
        return displayModels[nextIndex]
    }

    /// 切换到上一个宠物（用于详情页面的左右切换）
    func getPreviousPet(current: PetDisplayModel) -> PetDisplayModel? {
        guard let currentIndex = displayModels.firstIndex(where: { $0.id == current.id }) else {
            return nil
        }
        let previousIndex = currentIndex == 0 ? displayModels.count - 1 : currentIndex - 1
        return displayModels[previousIndex]
    }
}
