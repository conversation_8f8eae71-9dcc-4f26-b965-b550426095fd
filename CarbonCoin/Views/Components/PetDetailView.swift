//
//  PetDetailView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/17.
//

import SwiftUI

struct PetDetailView: View {
    @State private var currentDisplayModel: PetDisplayModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject  var viewModel: CarbonPetViewModel
    
    init(displayModel: PetDisplayModel) {
            _currentDisplayModel = State(initialValue: displayModel)
    }

    var body: some View {
        ZStack {
            // 背景
            CustomAngularGradient()

            ScrollView {
                VStack(spacing: 0) {
                    // 上半部分：宠物展示区域
                    PetDisplaySection(
                        displayModel: currentDisplayModel,
                        onPrevious: {
                            if let previous = viewModel.getPreviousDisplayModel(current: currentDisplayModel){
                                currentDisplayModel = previous
                            }
                        },
                        onNext: {
                            if let next = viewModel.getNextDisplayModel(current: currentDisplayModel){
                                currentDisplayModel = next
                            }
                        }
                    )

                    // 下半部分：装扮信息区域
                    PetOutfitSection()
                        .padding(.top, Theme.Spacing.lg)
                }
                .padding(.bottom, 100) // 为底部导航栏留出空间
            }
            .scrollContentBackground(.hidden)
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .foregroundColor(.white)
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .frame(width: 40, height: 40)
                        )
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                HStack(spacing: 8) {
                    // 碳币显示
                    HStack(spacing: 4) {
                        Image(systemName: "leaf.fill")
                            .foregroundColor(Color(hex: "B0EB67"))
                            .font(.caption)
                        Text("32.2k")
                            .font(.captionBrand)
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(Color.black.opacity(0.3))
                    )
                }
            }
        }
        .toolbarBackground(.clear, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
    }
}

// MARK: - 宠物展示区域
struct PetDisplaySection: View {
    let displayModel: PetDisplayModel
    let onPrevious: () -> Void
    let onNext: () -> Void

    var body: some View {
        ZStack {
            // 背景占位区域（为后续背景图预留空间）
            RoundedRectangle(cornerRadius: Theme.CornerRadius.xl)
                .fill(Color.black.opacity(0.2))
                .frame(height: 500)
                .padding(.horizontal, Theme.Spacing.md)

            VStack(spacing: Theme.Spacing.lg) {
                Spacer()

                // 宠物图片
                if let petImage = UIImage(named: displayModel.template.imageName) {
                    Image(uiImage: petImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 200, height: 200)
                        .scaleEffect(1.2) // 放大显示
                }

                Spacer()

                // 底部信息区域
                PetInfoBottomSection(
                    displayModel: displayModel,
                    onPrevious: onPrevious,
                    onNext: onNext
                )
            }
            .frame(height: 500)
            .padding(.horizontal, Theme.Spacing.md)
        }
    }
}

// MARK: - 宠物信息底部区域
struct PetInfoBottomSection: View {
    let displayModel: PetDisplayModel
    let onPrevious: () -> Void
    let onNext: () -> Void

    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            HStack {
                // 左侧功能按钮区域（占位）
                VStack(spacing: Theme.Spacing.sm) {
                    PlaceholderButton(icon: "face.smiling")
                    PlaceholderButton(icon: "tshirt")
                    PlaceholderButton(icon: "star")
                }

                Spacer()

                // 中间宠物信息
                VStack(spacing: Theme.Spacing.sm) {
                    // 宠物名称
                    Text(displayModel.template.name)
                        .font(.title1Brand)
                        .foregroundColor(.white)

                    // 星级评价
                    HStack(spacing: 4) {
                        ForEach(0..<displayModel.template.rarity, id: \.self) { _ in
                            Image(systemName: "star.fill")
                                .foregroundColor(.yellow)
                                .font(.title3)
                        }
                    }

                    // 描述信息和切换箭头
                    HStack(spacing: Theme.Spacing.md) {
                        // 左箭头
                        Button(action: onPrevious ) {
                            Image(systemName: "chevron.left")
                                .font(.title2)
                                .foregroundColor(.white.opacity(0.6))
                        }

                        // 描述文字
                        Text(displayModel.template.introduction )
                            .font(.bodyBrand)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                            .frame(maxWidth: 200)

                        // 右箭头
                        Button(action: onNext ) {
                            Image(systemName: "chevron.right")
                                .font(.title2)
                                .foregroundColor(.white.opacity(0.6))
                        }
                    }
                }

                Spacer()

                // 右侧等级显示
                VStack(spacing: Theme.Spacing.xs) {
                    Text("Lv\(displayModel.displayLevel)")
                        .font(.title1Brand)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .fill(Color.primaryGradient)
                        )
                }
            }
            .padding(.horizontal, Theme.Spacing.lg)
        }
        .padding(.vertical, Theme.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.xl)
                .fill(Color.black.opacity(0.4))
                .blur(radius: 10)
        )
        .padding(.horizontal, Theme.Spacing.md)
    }
}

// MARK: - 占位按钮组件
struct PlaceholderButton: View {
    let icon: String

    var body: some View {
        Button(action: {}) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.white.opacity(0.7))
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(Color.black.opacity(0.3))
                )
        }
        .advancedCardButtonStyle()
    }
}

// MARK: - 装扮信息区域
struct PetOutfitSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            // 标题栏
            HStack {
                Text("我的装扮")
                    .font(.title2Brand)
                    .foregroundColor(.white)

                Spacer()

                // 装扮数量指示器
                HStack(spacing: 4) {
                    Image(systemName: "square.grid.3x3")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                    Text("1/8")
                        .font(.captionBrand)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(Color.black.opacity(0.3))
                )

                // 添加按钮
                Button(action: {}) {
                    Image(systemName: "plus")
                        .font(.title3)
                        .foregroundColor(.white)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(Color.primaryGradient)
                        )
                }
                .advancedCardButtonStyle()
            }
            .padding(.horizontal, Theme.Spacing.lg)

            // 装扮列表（占位）
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: Theme.Spacing.md) {
                    // 当前装扮
                    OutfitItemView(
                        imageName: "pet_小碳", // 暂时使用固定值
                        title: "礼冠套装",
                        isSelected: true
                    )

                    // 其他装扮占位
                    ForEach(0..<3, id: \.self) { index in
                        OutfitItemView(
                            imageName: nil,
                            title: "花冠套装",
                            isSelected: false
                        )
                    }
                }
                .padding(.horizontal, Theme.Spacing.lg)
            }
        }
        .padding(.vertical, Theme.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.xl)
                .fill(Color.cardBackground.opacity(0.8))
        )
        .padding(.horizontal, Theme.Spacing.md)
    }
}

// MARK: - 装扮项目组件
struct OutfitItemView: View {
    let imageName: String?
    let title: String
    let isSelected: Bool

    var body: some View {
        VStack(spacing: Theme.Spacing.sm) {
            // 装扮图片
            ZStack {
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(isSelected ? Color.brandColor : Color.black.opacity(0.3))
                    .frame(width: 80, height: 80)

                if let imageName = imageName, let image = UIImage(named: imageName) {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 60, height: 60)
                } else {
                    // 占位图标
                    Image(systemName: "questionmark")
                        .font(.title)
                        .foregroundColor(.white.opacity(0.5))
                }

                // 选中状态指示器
                if isSelected {
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.white)
                                .background(
                                    Circle()
                                        .fill(Color.green)
                                        .frame(width: 20, height: 20)
                                )
                        }
                        Spacer()
                    }
                    .frame(width: 80, height: 80)
                    .padding(4)
                }
            }

            // 装扮名称
            Text(title)
                .font(.captionBrand)
                .foregroundColor(.white)
                .lineLimit(1)
        }
        .advancedCardButtonStyle()
    }
}

// MARK: - Preview
#Preview {
    let mockTemplate = PetTemplate(
        name: "小碳", 
        imageName: "pet_小碳", 
        rarity: 2,
        introduction: "草系小萌宠，浑身雪白，翠绿耳饰、颈叶、尾斑，小碳的圆眼睛亮闪闪，永远挂着甜甜的笑容，性格活泼又热情，会陪着你用碳币解锁各种互动玩法。 小碳活力满满，跑向宝藏的速度飞快，能够发现更多路上的东西。"
    )
    let mockUserPet = UserPet(templateName: "小碳", level: 5, experience: 250)
    let mockDisplayModel = PetDisplayModel(template: mockTemplate, userPet: mockUserPet)

    return PetDetailView(displayModel: mockDisplayModel)
}
