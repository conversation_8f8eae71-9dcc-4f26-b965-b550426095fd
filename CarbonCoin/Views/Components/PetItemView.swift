//  PetItemView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/17.
//

// TODO: 补充外轮廓、获得标签以及购买标签
import SwiftUI

struct PetItemView: View {
    let displayModel: PetDisplayModel
    
    private let cardWidth: CGFloat = 160
    private let cardHeight: CGFloat = 170
    
    var body: some View {
        NavigationLink(destination: PetDetailView(displayModel: displayModel)) {
            ZStack(alignment: .bottom) {
                // 背景和背景图片
                ZStack {
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .fill(LinearGradient.petItemBgColor)
                        .frame(width: cardWidth, height: cardHeight)
                        .shadow(color: .black.opacity(0.3), radius: 10, y: 5)

                    if (displayModel.isOwned ){
                        if let tag_bg = UIImage(named: "level_tag_bg") {
                            Image(uiImage: tag_bg)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 120, height: 40)
                                .offset(x:20, y: -cardHeight * 0.25)
                        }
                    }

                }
                .clipShape(RoundedRectangle(cornerRadius: Theme.CornerRadius.lg))
                .glassCard()

                // 宠物图片（允许超出边界）
                if let petImage = UIImage(named: displayModel.template.imageName) {
                    Image(uiImage: petImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: cardWidth * 1.05, height: cardHeight * 1.05)
                        .offset(y: -cardHeight * 0.06) // 向上偏移，使其突出
                        // 如果未获得，将图片转换为黑白
                        .colorMultiply(!displayModel.isOwned ? .black.opacity(0.5) : .white)
                }

                // 等级标签
                if( displayModel.isOwned ){
                    levelTag
                        .offset(x: cardWidth * 0.30, y: -cardHeight * 0.25)
                        .frame(width: cardWidth, height: cardHeight)
                }
                
                // 底部信息栏
                bottomInfoBar
            }
            .frame(width: cardWidth, height: cardHeight)
            // 添加遮罩：如果未获得，整个卡片变灰
            .overlay(
               !displayModel.isOwned ?
               Color.black.opacity(0.3)
                   .clipShape(RoundedRectangle(cornerRadius: Theme.CornerRadius.lg))
                   .frame(width: cardWidth, height: cardHeight)
               : nil
            )
            
        }
        .buttonStyle(PlainButtonStyle()) // 移除默认NavigationLink样式
        .advancedCardButtonStyle()
    }
    
    // 等级标签
    private var levelTag: some View {
        ZStack {
            Text("Lv\(displayModel.displayLevel)")
                .font(.title2Brand)
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 5)
                .rotationEffect(.degrees(45))
        }
    }
    
    // 底部信息栏
    private var bottomInfoBar: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(displayModel.template.name)
                    .font(.subheadline)
                    .foregroundColor(.white)

                HStack(spacing: 2) {
                    ForEach(0..<displayModel.template.rarity, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.system(size: 12))
                    }
                }
            }
            
            Spacer()

            OwnershipBadge(isOwned: displayModel.isOwned)
        }
        .padding(12)
        .background(
            Color.black.opacity(0.6)
                .blur(radius: 4)
        )
        .clipShape(RoundedCorner(radius: 20, corners: [.bottomLeft, .bottomRight]))
    }
}

// MARK: - Preview
#Preview {
    let mockTemplateOwned = PetTemplate(
        name: "小碳", 
        imageName: "pet_小碳", 
        rarity: 2,
        introduction: "草系小萌宠，浑身雪白，翠绿耳饰、颈叶、尾斑，小碳的圆眼睛亮闪闪，永远挂着甜甜的笑容，性格活泼又热情，会陪着你用碳币解锁各种互动玩法。 小碳活力满满，跑向宝藏的速度飞快，能够发现更多路上的东西。"
    )
    let mockUserPet = UserPet(templateName: "小碳", level: 5, experience: 250)
    let mockDisplayModelOwned = PetDisplayModel(template: mockTemplateOwned, userPet: mockUserPet)

    let mockTemplateNotOwned = PetTemplate(name: "贰负", imageName: "pet_贰负", rarity: 3)
    let mockDisplayModelNotOwned = PetDisplayModel(template: mockTemplateNotOwned, userPet: nil)

    return ZStack {
        Color.black.ignoresSafeArea()
        VStack(spacing: 20) {
            PetItemView(displayModel: mockDisplayModelOwned)
            PetItemView(displayModel: mockDisplayModelNotOwned)
        }
    }
}

// Helper for specific corner radius
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}
