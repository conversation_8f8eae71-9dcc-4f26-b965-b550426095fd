//  PetListView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/17.
//

import SwiftUI

struct PetListView: View {
    @ObservedObject var viewModel: CarbonPetViewModel
    
    private let columns = [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ]
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: 20) {
                ForEach(viewModel.displayModels) { displayModel in
                    NavigationLink(destination: PetDetailView(displayModel: displayModel)) {
                        PetItemView(displayModel: displayModel)
                    }
                    .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式
                }
            }
            .padding()

        }
        
    }
}

#Preview {
    let viewModel = CarbonPetViewModel()
    return PetListView(viewModel: viewModel)
        .background(Color.black.ignoresSafeArea())
}
