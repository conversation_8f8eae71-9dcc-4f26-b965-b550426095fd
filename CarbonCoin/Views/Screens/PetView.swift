//
//  PetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct PetView: View {
    @StateObject private var viewModel = CarbonPetViewModel()

    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                CustomAngularGradient()
                
                // 宠物列表
                PetListView()
                    .environmentObject(viewModel)
            }
            .navigationTitle("宠物图鉴")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .environmentObject(viewModel)
        }
    }
}

#Preview {
    PetView()
}
